# Zeeguros Green Color Implementation

## Overview

The official Zeeguros green color `#06BE60` has been added to the platform's design system. This color is used throughout the Zeeguros website and should be used for primary actions and brand elements.

## Color Values

- **Hex**: `#06BE60`
- **HSL**: `hsl(147, 75%, 56%)`
- **RGB**: `rgb(6, 190, 96)`

## CSS Variables

The color is available as CSS custom properties:

```css
:root {
  --zeeguros-green: 147 75% 56%;
  --zeeguros-green-foreground: 0 0% 9%;
}

.dark {
  --zeeguros-green: 147 75% 56%;
  --zeeguros-green-foreground: 0 0% 98%;
}
```

## Tailwind CSS Classes

### Background Colors
- `bg-zeeguros-green` - Solid green background
- `hover:bg-zeeguros-green:hover` - Green background on hover
- `bg-zeeguros-green/90` - Green background with 90% opacity

### Text Colors
- `text-zeeguros-green` - Green text color
- `hover:text-zeeguros-green:hover` - Green text on hover

### Border Colors
- `border-zeeguros-green` - Green border

### Focus Ring
- `focus:ring-zeeguros-green:focus` - Green focus ring

## Tailwind Config

The color is also available in the Tailwind configuration:

```typescript
zeeguros: {
  green: '#06BE60',
  // ... other colors
},
'zeeguros-green': {
  DEFAULT: 'hsl(var(--zeeguros-green))',
  foreground: 'hsl(var(--zeeguros-green-foreground))'
},
```

## Zeeguros Button Style

A special button class `.btn-zeeguros` has been created to match the exact style from the Zeeguros website:

```css
.btn-zeeguros {
  @apply bg-zeeguros-green text-black font-semibold px-6 py-3 rounded-lg border-2 border-black
         hover:bg-zeeguros-green/90 transition-all duration-200
         focus:outline-none focus:ring-2 focus:ring-zeeguros-green focus:ring-offset-2;
}

.btn-zeeguros:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
```

### Button Features:
- **Green background** (`#06BE60`)
- **Black text** for optimal contrast
- **Rounded corners** (`rounded-lg`)
- **Black border** (2px)
- **Hover effects**: Slight lift and shadow
- **Focus ring** for accessibility

## Usage Examples

### Zeeguros Style Button (Recommended)
```tsx
<Button className="btn-zeeguros">
  ¡Ahorrar ya!
</Button>
```

### Custom Button with Individual Classes
```tsx
<Button className="bg-zeeguros-green hover:bg-zeeguros-green/90 text-black border-2 border-black rounded-lg">
  Guardar Cambios
</Button>
```

### Link with Green Text
```tsx
<Link className="text-zeeguros-green hover:text-zeeguros-green/80">
  Ver más
</Link>
```

### Card with Green Border
```tsx
<Card className="border-zeeguros-green">
  <CardContent>
    Content here
  </CardContent>
</Card>
```

## Current Implementation

The Zeeguros green color has been applied to:

1. **Settings Page**:
   - "Guardar Cambios" button in profile section
   - "Actualizar Contraseña" button in password section

2. **Policies Page**:
   - "Añadir Nueva Póliza" button

## Best Practices

1. **Primary Actions**: Use Zeeguros green for primary call-to-action buttons
2. **Brand Elements**: Use for elements that need to reinforce the Zeeguros brand
3. **Accessibility**: Always ensure sufficient contrast when using the green color
4. **Consistency**: Use the same green across all brand touchpoints

## Accessibility

The Zeeguros green `#07F97F` provides good contrast ratios:
- Against white background: High contrast (suitable for text and buttons)
- Against dark backgrounds: Excellent visibility

Always test color combinations for accessibility compliance.
