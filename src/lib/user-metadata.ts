import { User } from '@supabase/supabase-js';
import { createClient } from '@/utils/supabase/client';
import { useState, useEffect } from 'react';

/**
 * Safely extracts user name from Supabase user metadata
 * Handles different possible metadata structures
 */
export function extractUserName(user: User | null): { firstName: string, lastName: string, fullName: string } {
  if (!user) {
    return { firstName: '', lastName: '', fullName: 'Usuario' };
  }

  let firstName = '';
  let lastName = '';

  if (user.user_metadata) {
    if (typeof user.user_metadata.first_name === 'string') {
      firstName = user.user_metadata.first_name;
    } else if (typeof user.user_metadata.firstName === 'string') {
      firstName = user.user_metadata.firstName;
    } else if (typeof user.user_metadata.name === 'string') {
      const nameParts = user.user_metadata.name.split(' ');
      firstName = nameParts[0] || '';
      if (nameParts.length > 1) {
        lastName = nameParts.slice(1).join(' ');
      }
    }

    if (!lastName && typeof user.user_metadata.last_name === 'string') {
      lastName = user.user_metadata.last_name;
    } else if (!lastName && typeof user.user_metadata.lastName === 'string') {
      lastName = user.user_metadata.lastName;
    }
  }

  if (!firstName && user.email) {
    const emailName = user.email.split('@')[0] || '';
    const nameParts = emailName.split(/[._-]/);
    firstName = nameParts.map(part =>
      part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
    ).join(' ');
  }

  const fullName = [firstName, lastName].filter(Boolean).join(' ') || 'Usuario';

  return { firstName, lastName, fullName };
}

export function useUser() {
  const [user, setUser] = useState<User | null>(null);
  const [role, setRole] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();

    const fetchUser = async () => {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error) {
        console.error("Error fetching user:", error);
        setUser(null);
        setRole(null);
      } else {
        setUser(user);
        if (user?.user_metadata?.role) {
          setRole(user.user_metadata.role as string);
        } else {
          setRole("customer"); // Default role if not set
        }
      }
      setLoading(false);
    };

    fetchUser();

    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        setUser(session?.user || null);
        if (session?.user?.user_metadata?.role) {
          setRole(session.user.user_metadata.role as string);
        } else {
          setRole("customer");
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setRole(null);
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  return { user, role, loading };
}
