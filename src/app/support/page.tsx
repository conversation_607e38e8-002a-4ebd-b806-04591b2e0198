import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Mail, Phone } from "lucide-react";

export default function SupportPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-semibold tracking-tight">Soporte</h1>
        <p className="text-muted-foreground">
          ¿Necesitas ayuda? Estamos aquí para asistirte.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Correo Electrónico
            </CardTitle>
            <CardDescription>
              Envíanos un correo y te responderemos en 24 horas.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className="btn-zeeguros w-full"
              asChild
            >
              <a href="mailto:<EMAIL>">
                <EMAIL>
              </a>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Teléfono
            </CardTitle>
            <CardDescription>
              Llámanos de lunes a viernes de 9:00 a 18:00.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              className="btn-zeeguros w-full"
              asChild
            >
              <a href="https://wa.me/34665444222" target="_blank" rel="noopener noreferrer">
                +34 900 123 456
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
