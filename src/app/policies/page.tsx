import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Plus, ArrowRight, Calendar, Car, Bike } from "lucide-react";
import Link from "next/link";
import { userDataContext } from "./layout";
import { VehicleType } from "@prisma/client";
import { db } from "@/lib/db";

export default async function PoliciesPage() {
  // Get user data from context
  const userId = userDataContext.user?.id || '';
  const fullName = userDataContext.userName;

  // Get policies data using Prisma
  const rawPolicies = await db.policy.findMany({
    where: {
      userId: userId
    },
    include: {
      vehicle: true
    },
    orderBy: {
      createdAt: 'desc'
    }
  });

  // Define policy type
  interface Policy {
    id: string;
    type: string;
    icon: JSX.Element;
    policyNumber: string;
    effectiveDate: string;
    expirationDate: string;
    insuredName: string;
    carrier: string;
    status: string;
  }

  // Transform policies data for display
  const policies: Policy[] = rawPolicies && rawPolicies.length > 0 ?
    rawPolicies.map((policy: any) => {
      let type = "Auto Insurance";
      let icon = <Car className="h-5 w-5" />;

      if (policy.vehicle.type === "CAR") {
        type = "Car Insurance";
        icon = <Car className="h-5 w-5" />;
      } else if (policy.vehicle.type === "MOTORCYCLE") {
        type = "Motorcycle Insurance";
        icon = <Bike className="h-5 w-5" />;
      }

      // Format dates safely
      const startDate = new Date(policy.startDate || Date.now());
      const endDate = new Date(policy.endDate || Date.now());
      const startDateStr = startDate.toISOString().split('T')[0];
      const endDateStr = endDate.toISOString().split('T')[0];

      return {
        id: policy.id,
        type,
        icon,
        policyNumber: policy.id.substring(0, 8).toUpperCase(),
        effectiveDate: startDateStr,
        expirationDate: endDateStr,
        insuredName: fullName,
        carrier: "Zeeguros",
        status: "Activa",
      };
    }) : [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold tracking-tight">
          Mis Pólizas
        </h1>
        <Button asChild className="btn-zeeguros">
          <Link href="/policies/new-policy">
            <Plus className="h-4 w-4 mr-2" />
            Añadir Nueva Póliza
          </Link>
        </Button>
      </div>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList>
          <TabsTrigger value="all">Todas</TabsTrigger>
          <TabsTrigger value="auto">Coche</TabsTrigger>
          <TabsTrigger value="home">Moto</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {policies.length > 0 ? (
            policies.map((policy) => (
              <Card
                key={policy.id}
                className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-zeeguros-green bg-gradient-to-r from-white to-gray-50/30"
              >
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                    <div className="flex gap-4 items-center">
                      <div className="rounded-xl bg-zeeguros-green/10 p-3 flex-shrink-0 border border-zeeguros-green/20">
                        <div className="text-zeeguros-green">
                          {policy.icon}
                        </div>
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-semibold text-gray-900 text-lg">{policy.type}</h3>
                        <p className="text-sm font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded-md inline-block">
                          {policy.policyNumber}
                        </p>
                      </div>
                    </div>
                    <div className="flex flex-col md:flex-row gap-4 md:items-center">
                      <div className="flex items-center gap-2 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                        <Calendar className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-700">
                          Vence: {policy.expirationDate}
                        </span>
                      </div>
                      <Button className="btn-zeeguros-back" size="sm" asChild>
                        <Link href={`/policies/${policy.id}`}>
                          Ver Detalles
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card className="bg-white">
              <CardContent className="p-6 flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground text-center">No hay pólizas disponibles.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="auto" className="space-y-4">
          {policies.filter((policy) => policy.type === "Car Insurance").length > 0 ? (
            policies
              .filter((policy) => policy.type === "Car Insurance")
              .map((policy) => (
                <Card
                  key={policy.id}
                  className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-zeeguros-green bg-gradient-to-r from-white to-gray-50/30"
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                      <div className="flex gap-4 items-center">
                        <div className="rounded-xl bg-zeeguros-green/10 p-3 flex-shrink-0 border border-zeeguros-green/20">
                          <div className="text-zeeguros-green">
                            {policy.icon}
                          </div>
                        </div>
                        <div className="space-y-1">
                          <h3 className="font-semibold text-gray-900 text-lg">{policy.type}</h3>
                          <p className="text-sm font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded-md inline-block">
                            {policy.policyNumber}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col md:flex-row gap-4 md:items-center">
                        <div className="flex items-center gap-2 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                          <Calendar className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-700">
                            Vence: {policy.expirationDate}
                          </span>
                        </div>
                        <Button className="btn-zeeguros-back" size="sm" asChild>
                          <Link href={`/policies/${policy.id}`}>
                            Ver Detalles
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
          ) : (
            <Card className="bg-white">
              <CardContent className="p-6 flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground text-center">No hay pólizas de coche disponibles.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="home" className="space-y-4">
          {policies.filter((policy) => policy.type === "Motorcycle Insurance").length > 0 ? (
            policies
              .filter((policy) => policy.type === "Motorcycle Insurance")
              .map((policy) => (
                <Card
                  key={policy.id}
                  className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-zeeguros-green bg-gradient-to-r from-white to-gray-50/30"
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                      <div className="flex gap-4 items-center">
                        <div className="rounded-xl bg-zeeguros-green/10 p-3 flex-shrink-0 border border-zeeguros-green/20">
                          <div className="text-zeeguros-green">
                            {policy.icon}
                          </div>
                        </div>
                        <div className="space-y-1">
                          <h3 className="font-semibold text-gray-900 text-lg">{policy.type}</h3>
                          <p className="text-sm font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded-md inline-block">
                            {policy.policyNumber}
                          </p>
                        </div>
                      </div>
                      <div className="flex flex-col md:flex-row gap-4 md:items-center">
                        <div className="flex items-center gap-2 bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                          <Calendar className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-700">
                            Vence: {policy.expirationDate}
                          </span>
                        </div>
                        <Button className="btn-zeeguros-back" size="sm" asChild>
                          <Link href={`/policies/${policy.id}`}>
                            Ver Detalles
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
          ) : (
            <Card className="bg-white">
              <CardContent className="p-6 flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground text-center">No hay pólizas de moto disponibles.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
