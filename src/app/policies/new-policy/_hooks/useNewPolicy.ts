import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { createClient } from "@/utils/supabase/client";

export type PolicyData = {
  // Policy details
  policyNumber: string;
  policyType: string;
  effectiveDate: string;
  expirationDate: string;
  paymentMethod: string;
  premium: string;

  // Insured details
  insuredName: string;
  insuredAddress: string;
  insuredEmail: string;
  insuredPhone: string;
  insuredDNI: string;
  insuredGender: string;
  insuredPostalCode: string;
  insuredCountry: string;

  // Vehicle details
  vehicleMake: string;
  vehicleModel: string;
  vehicleYear: string;
  vehicleVin: string;
  vehiclePlate: string;
  vehicleFuel: string;
  vehicleUse: string;
  vehicleStorage: string;
  vehicleAnnualKm: string;

  // Coverage details
  coverages: {
    name: string;
    included: boolean;
    limit?: string;
  }[];
};

export const useNewPolicy = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [policyData, setPolicyData] = useState<PolicyData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [auctionDuration, setAuctionDuration] = useState<24 | 48 | 72 | null>(null);
  const { toast } = useToast();
  const supabase = createClient();
  const [user, setUser] = useState<any>(null);

  // Get the user on component mount
  useEffect(() => {
    async function getUser() {
      try {
        const { data } = await supabase.auth.getUser();
        setUser(data.user);
      } catch (error) {
        console.error("Error getting user:", error);
      }
    }

    getUser();
  }, [supabase.auth]);

  // Load extracted data from localStorage or use mock data
  useEffect(() => {
    if (uploadedFile && currentStep === 1 && !policyData) {
      // Try to get data from localStorage (where Gemini API stored it)
      const savedData = localStorage.getItem("extractedPolicyData");

      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);

          // Ensure we have a coverages array, even if not in the parsed data
          if (!parsedData.coverages) {
            parsedData.coverages = getDefaultCoverages();
          }

          setPolicyData(parsedData);
          return;
        } catch (e) {
          console.error("Error parsing saved policy data:", e);
        }
      }

      // Fallback to mock data if no extracted data is available
      const mockData = getMockPolicyData();

      // Add default coverages if not present
      if (!mockData.coverages) {
        mockData.coverages = getDefaultCoverages();
      }

      setPolicyData(mockData);
    }
  }, [uploadedFile, currentStep, policyData]);

  const getDefaultCoverages = () => {
    return [
      { name: "Responsabilidad Civil Obligatoria", included: true, limit: "70.000.000€" },
      { name: "Responsabilidad Civil Voluntaria", included: true, limit: "50.000.000€" },
      { name: "Defensa Jurídica", included: true, limit: "1.000€" },
      { name: "Reclamación de Daños", included: true, limit: "1.000€" },
      { name: "Lunas", included: true, limit: "Reposición" },
      { name: "Robo", included: false },
      { name: "Incendio", included: false },
      { name: "Daños Propios", included: false },
      { name: "Asistencia en Viaje", included: true },
      { name: "Accidentes del Conductor", included: true, limit: "25.000€" },
      { name: "Vehículo de Sustitución", included: false }
    ];
  };

  const getMockPolicyData = (): PolicyData => {
    return {
      // Policy details
      policyNumber: "POL-" + Math.floor(10000000 + Math.random() * 90000000),
      policyType: "Auto Insurance",
      effectiveDate: new Date().toISOString().split('T')[0],
      expirationDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
      paymentMethod: "Anual",
      premium: "450",

      // Insured details
      insuredName: user?.user_metadata?.first_name ? `${user.user_metadata.first_name} ${user.user_metadata.last_name || ''}` : "Juan Pérez",
      insuredAddress: "Calle Principal 123",
      insuredEmail: user?.email || "<EMAIL>",
      insuredPhone: user?.user_metadata?.phone || "612345678",
      insuredDNI: "12345678A",
      insuredGender: "Masculino",
      insuredPostalCode: "28001",
      insuredCountry: "España",

      // Vehicle details
      vehicleMake: "Toyota",
      vehicleModel: "Corolla",
      vehicleYear: "2020",
      vehicleVin: "1HGCM82633A123456",
      vehiclePlate: "1234 ABC",
      vehicleFuel: "Gasolina",
      vehicleUse: "Particular",
      vehicleStorage: "Garaje particular",
      vehicleAnnualKm: "15000",

      // Coverage details
      coverages: getDefaultCoverages()
    };
  };

  const handleFileUploadComplete = (file: File) => {
    setUploadedFile(file);
  };

  const handleDataUpdate = (updatedData: PolicyData) => {
    setPolicyData(updatedData);
  };

  const handleAuctionDurationSelect = (duration: 24 | 48 | 72) => {
    setAuctionDuration(duration);
  };

  const handleContinue = () => {
    setCurrentStep((prev) => prev + 1);
  };

  const handleBack = () => {
    setCurrentStep((prev) => Math.max(0, prev - 1));
  };

  const handleSubmit = async () => {
    if (!policyData || !auctionDuration) {
      toast({
        variant: "destructive",
        title: "Falta información",
        description: "Por favor completa todos los campos requeridos.",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Demo mode simulation removed

      // Real submission for logged in users
      if (!user) {
        toast({
          variant: "destructive",
          title: "Sesión no iniciada",
          description: "Debes iniciar sesión para guardar la póliza.",
        });
        return;
      }

      // Upload the policy document to storage if available
      if (uploadedFile) {
        const userId = user.id;
        const filePath = `${userId}/${Date.now()}_${uploadedFile.name.replace(/\s+/g, '_')}`;

        const { error: uploadError } = await supabase.storage
          .from('policy_documents')
          .upload(filePath, uploadedFile);

        if (uploadError) {
          throw new Error(`Error subiendo documento: ${uploadError.message}`);
        }

        // Document uploaded successfully
        console.log("Document uploaded to:", filePath);
      }

      // Note: The following code would work if the database schema was properly set up
      // For now, we'll just simulate success since we can't actually connect to the database

      // Simulate database operations
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Póliza registrada con éxito",
        description: "Tu póliza ha sido guardada y la subasta ha sido creada.",
      });

      // Clean up localStorage
      localStorage.removeItem("extractedPolicyData");

      setCurrentStep(4); // Move to success step
    } catch (error: any) {
      console.error("Error en registro de póliza:", error);
      toast({
        variant: "destructive",
        title: "Error al guardar póliza",
        description: error.message || "Hubo un error al guardar tu póliza. Por favor intenta de nuevo.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    currentStep,
    policyData,
    isSubmitting,
    auctionDuration,
    handleFileUploadComplete,
    handleDataUpdate,
    handleAuctionDurationSelect,
    handleContinue,
    handleBack,
    handleSubmit,
  };
};
