"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft, ArrowRight, ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { PolicyData } from "../_hooks/useNewPolicy";
import { PhoneInput } from "@/components/ui/phone-input";

// Define the validation schema with Zod
const policySchema = z.object({
  // Policy details
  policyNumber: z.string().min(1, "Número de póliza es requerido"),
  policyType: z.string().min(1, "Tipo de póliza es requerido"),
  effectiveDate: z.string().min(1, "Fecha de inicio es requerida"),
  expirationDate: z.string().min(1, "Fecha de vencimiento es requerida"),
  paymentMethod: z.string().min(1, "Forma de pago es requerida"),
  premium: z.string().min(1, "Prima es requerida"),

  // Insured details
  insuredName: z.string().min(1, "Nombre del asegurado es requerido"),
  insuredAddress: z.string().min(1, "Dirección es requerida"),
  insuredEmail: z.string().email("Correo electrónico inválido"),
  insuredPhone: z.string().min(1, "Teléfono es requerido"),
  insuredDNI: z.string().min(1, "DNI/NIE/NIF es requerido"),
  insuredGender: z.string().optional(),
  insuredPostalCode: z.string().min(1, "Código postal es requerido"),
  insuredCountry: z.string().min(1, "País es requerido"),

  // Vehicle details
  vehicleMake: z.string().min(1, "Marca es requerida"),
  vehicleModel: z.string().min(1, "Modelo es requerido"),
  vehicleYear: z.string().min(1, "Año es requerido"),
  vehicleVin: z.string().min(1, "Número VIN es requerido"),
  vehiclePlate: z.string().min(1, "Matrícula es requerida"),
  vehicleFuel: z.string().min(1, "Combustible es requerido"),
  vehicleUse: z.string().min(1, "Uso es requerido"),
  vehicleStorage: z.string().min(1, "Lugar de aparcamiento es requerido"),
  vehicleAnnualKm: z.string().min(1, "Kilometraje anual es requerido"),

  // Coverages - this is a bit complex for Zod, so we'll validate it separately
  coverages: z.array(
    z.object({
      name: z.string(),
      included: z.boolean(),
      limit: z.string().optional(),
    }),
  ),
});

interface PolicyDataFormProps {
  initialData: PolicyData;
  onDataUpdate: (data: PolicyData) => void;
  onBack: () => void;
  onContinue: () => void;
}

export function PolicyDataForm({
  initialData,
  onDataUpdate,
  onBack,
  onContinue,
}: PolicyDataFormProps) {
  const [activeTab, setActiveTab] = useState("policy");
  // Ensure initialData.insuredPhone is in E.164 format or empty string
  const [phoneNumber, setPhoneNumber] = useState(
    initialData.insuredPhone?.startsWith('+') ? initialData.insuredPhone : ''
  );

  const form = useForm<PolicyData>({
    resolver: zodResolver(policySchema),
    defaultValues: initialData,
    mode: "onChange",
  });

  const onSubmit = (data: PolicyData) => {
    onDataUpdate(data);
    onContinue();
  };

  // Group coverages by category for easier display
  const coverageCategories = {
    "Responsabilidad Civil": [
      "Responsabilidad Civil Obligatoria",
      "Responsabilidad Civil Voluntaria",
    ],

    "Defensa Jurídica": ["Defensa Jurídica", "Reclamación de Daños"],

    "Daños Propios": ["Lunas", "Robo", "Incendio", "Daños Propios"],

    Asistencia: ["Asistencia en Viaje", "Vehículo de Sustitución"],

    Conductor: ["Accidentes del Conductor"],
  };

  const getCoveragesByCategory = (category: string) => {
    const coverageNames =
      coverageCategories[category as keyof typeof coverageCategories] || [];
    return form
      .watch("coverages")
      .filter((c) => coverageNames.includes(c.name));
  };

  const updateCoverageState = (coverageName: string, included: boolean) => {
    const coverages = form.getValues("coverages");
    const updatedCoverages = coverages.map((c) =>
      c.name === coverageName ? { ...c, included } : c,
    );
    form.setValue("coverages", updatedCoverages, { shouldValidate: true });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6"
      >
        <div className="space-y-2">
          <h2
            className="text-2xl font-semibold tracking-tight"
          >
            Revisar Datos de la Póliza
          </h2>
          <p className="text-muted-foreground">
            Hemos extraído la siguiente información de tu póliza. Por favor
            revisa y corrige cualquier error.
          </p>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="policy">
              Póliza
            </TabsTrigger>
            <TabsTrigger value="insured">
              Asegurado
            </TabsTrigger>
            <TabsTrigger value="vehicle">
              Vehículo
            </TabsTrigger>
            <TabsTrigger value="coverages">
              Coberturas
            </TabsTrigger>
          </TabsList>

          <TabsContent value="policy" className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="policyNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Número de Póliza
                        </FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="policyType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tipo de Póliza</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona tipo de póliza"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem
                              value="Auto Insurance"
                            >
                              Seguro de Auto
                            </SelectItem>
                            <SelectItem value="Todo Riesgo">
                              Todo Riesgo
                            </SelectItem>
                            <SelectItem value="Terceros">
                              Terceros
                            </SelectItem>
                            <SelectItem
                              value="Terceros Ampliado"
                            >
                              Terceros Ampliado
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="effectiveDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Fecha de Inicio
                        </FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expirationDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Fecha de Vencimiento
                        </FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Forma de Pago</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona forma de pago"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Anual">
                              Anual
                            </SelectItem>
                            <SelectItem value="Semestral">
                              Semestral
                            </SelectItem>
                            <SelectItem value="Trimestral">
                              Trimestral
                            </SelectItem>
                            <SelectItem value="Mensual">
                              Mensual
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="premium"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Prima Anual (€)
                        </FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Atrás
              </Button>
              <Button
                type="button"
                className="btn-zeeguros"
                onClick={() => setActiveTab("insured")}
              >
                Siguiente
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="insured" className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="insuredName"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>
                          Nombre Completo
                        </FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuredDNI"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>DNI/NIE/NIF</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuredGender"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Género</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona género"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Masculino">
                              Masculino
                            </SelectItem>
                            <SelectItem value="Femenino">
                              Femenino
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuredAddress"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Dirección</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuredPostalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Código Postal</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuredCountry"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>País</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona país"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="España">
                              España
                            </SelectItem>
                            <SelectItem value="Francia">
                              Francia
                            </SelectItem>
                            <SelectItem value="Portugal">
                              Portugal
                            </SelectItem>
                            <SelectItem value="Italia">
                              Italia
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuredEmail"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="insuredPhone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Teléfono</FormLabel>
                        <FormControl>
                          <PhoneInput
                            id="insuredPhone"
                            placeholder="Introduce tu número de teléfono"
                            defaultCountry="ES"
                            required
                            value={phoneNumber}
                            onChange={(value) => {
                              // value will be in E.164 format or undefined
                              const newValue = value || '';
                              setPhoneNumber(newValue);
                              form.setValue("insuredPhone", newValue, { shouldDirty: true });
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <input type="hidden" name="insuredPhone" value={phoneNumber} />

                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setActiveTab("policy")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Atrás
              </Button>
              <Button
                type="button"
                className="btn-zeeguros"
                onClick={() => setActiveTab("vehicle")}
              >
                Siguiente
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="vehicle" className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vehicleMake"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Marca</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleModel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Modelo</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleYear"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Año</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleFuel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Combustible</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona combustible"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Gasolina">
                              Gasolina
                            </SelectItem>
                            <SelectItem value="Diesel">
                              Diesel
                            </SelectItem>
                            <SelectItem value="Híbrido">
                              Híbrido
                            </SelectItem>
                            <SelectItem value="Eléctrico">
                              Eléctrico
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehiclePlate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Matrícula</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleVin"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bastidor (VIN)</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleUse"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Uso del Vehículo
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona uso"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Particular">
                              Particular
                            </SelectItem>
                            <SelectItem value="Profesional">
                              Profesional
                            </SelectItem>
                            <SelectItem value="Mixto">
                              Mixto
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleStorage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Lugar de Aparcamiento
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona aparcamiento"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Calle">
                              Calle
                            </SelectItem>
                            <SelectItem
                              value="Garaje particular"
                            >
                              Garaje particular
                            </SelectItem>
                            <SelectItem
                              value="Garaje comunitario"
                            >
                              Garaje comunitario
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="vehicleAnnualKm"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Kilometraje Anual
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue
                                placeholder="Selecciona kilometraje"
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="5000">
                              Menos de 5.000 km
                            </SelectItem>
                            <SelectItem value="10000">
                              5.000 - 10.000 km
                            </SelectItem>
                            <SelectItem value="15000">
                              10.000 - 15.000 km
                            </SelectItem>
                            <SelectItem value="20000">
                              15.000 - 20.000 km
                            </SelectItem>
                            <SelectItem value="25000">
                              Más de 20.000 km
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setActiveTab("insured")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Atrás
              </Button>
              <Button
                type="button"
                className="btn-zeeguros"
                onClick={() => setActiveTab("coverages")}
              >
                Siguiente
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>

          <TabsContent
            value="coverages"
            className="space-y-6"
          >
            <Card>
              <CardContent className="pt-6">
                <Accordion
                  type="multiple"
                  className="w-full"
                >
                  {Object.entries(coverageCategories).map(([category, _]) => (
                    <AccordionItem
                      value={category}
                      key={category}
                    >
                      <AccordionTrigger
                        className="text-base font-medium"
                      >
                        {category}
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          {getCoveragesByCategory(category).map(
                            (coverage, index) => (
                              <div
                                key={coverage.name}
                                className="flex items-center justify-between py-2"
                              >
                                <div>
                                  <p className="font-medium">
                                    {coverage.name}
                                  </p>
                                  {coverage.limit && (
                                    <p
                                      className="text-sm text-muted-foreground"
                                    >
                                      Límite: {coverage.limit}
                                    </p>
                                  )}
                                </div>
                                <div
                                  className="flex items-center space-x-2"
                                >
                                  <span
                                    className={
                                      coverage.included
                                        ? "text-green-600"
                                        : "text-gray-400"
                                    }
                                  >
                                    {coverage.included
                                      ? "INCLUIDO"
                                      : "NO INCLUIDO"}
                                  </span>
                                  <Switch
                                    checked={coverage.included}
                                    onCheckedChange={(checked) =>
                                      updateCoverageState(
                                        coverage.name,
                                        checked,
                                      )
                                    }
                                  />
                                </div>
                              </div>
                            ),
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </CardContent>
            </Card>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setActiveTab("vehicle")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Atrás
              </Button>
              <Button type="submit" className="btn-zeeguros">
                Continuar
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </form>
    </Form>
  );
}
