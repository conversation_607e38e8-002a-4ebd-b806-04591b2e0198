"use client";

import { Check } from "lucide-react";

interface Step {
  title: string;
  description: string;
}

interface PolicyStepperProps {
  currentStep: number;
  steps: Step[];
}

export function PolicyStepper({
  currentStep,
  steps,
}: PolicyStepperProps) {
  return (
    <div className="w-full">
      <ol
        className="grid grid-cols-5 text-sm font-medium text-center text-muted-foreground"
      >
        {steps.map((step, index) => {
          const isActive = index === currentStep;
          const isCompleted = index < currentStep;
          return (
            <li key={index} className="relative">
              {index > 0 && (
                <div className="flex items-center">
                  <div
                    className={`flex-grow border-t ${index <= currentStep ? "border-primary" : "border-muted-foreground/30"} transition-colors duration-200`}
                  ></div>
                </div>
              )}
              <div
                className="flex flex-col items-center gap-1"
              >
                <div
                  className={`z-10 flex items-center justify-center w-10 h-10 rounded-full transition-colors ${
                    isActive
                      ? "bg-primary text-white"
                      : isCompleted
                        ? "bg-primary text-white"
                        : "bg-muted-foreground/10 text-muted-foreground"
                  }`}
                >
                  {isCompleted ? (
                    <Check className="w-6 h-6" />
                  ) : (
                    index + 1
                  )}
                </div>
                <span className="text-xs sm:text-sm">
                  {step.title}
                </span>
              </div>
            </li>
          );
        })}
      </ol>
      <div
        className="mt-2 text-sm text-center text-muted-foreground"
      >
        {steps[currentStep]?.description}
      </div>
    </div>
  );
}
