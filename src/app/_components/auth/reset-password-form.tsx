"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { AlertCircle, Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";
import { createClient } from "@/utils/supabase/client";
// Logo is now in public directory

const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, { message: "La contraseña debe tener al menos 8 caracteres" })
      .regex(/[A-Z]/, {
        message: "La contraseña debe contener al menos una letra mayúscula",
      })
      .regex(/[a-z]/, {
        message: "La contraseña debe contener al menos una letra minúscula",
      })
      .regex(/[0-9]/, {
        message: "La contraseña debe contener al menos un número",
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Las contraseñas no coinciden",
    path: ["confirmPassword"],
  });

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export function ResetPasswordForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetComplete, setResetComplete] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Check if we have a valid hash or code in the URL
  useEffect(() => {
    const checkResetToken = async () => {
      try {
        // Check for hash in URL or code parameter
        const hash = window.location.hash.substring(1);
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');

        console.log('Reset password page loaded with:', { hash, code });

        if (!hash && !code) {
          console.error('No hash or code found in URL');
          toast({
            variant: "destructive",
            title: "Error",
            description: "Enlace de restablecimiento inválido o expirado.",
          });
          setTimeout(() => {
            window.location.href = "/login";
          }, 3000);
        }
      } catch (error) {
        console.error('Error checking reset token:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Ocurrió un error al verificar el enlace de restablecimiento.",
        });
      }
    };

    checkResetToken();
  }, [toast]);

  async function onSubmit(data: ResetPasswordFormValues) {
    setIsLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: data.password,
      });

      if (error) {
        console.error('Error updating password:', error);
        toast({
          variant: "destructive",
          title: "Error",
          description: `No se pudo actualizar la contraseña: ${error.message || 'Error desconocido'}`,
        });
        return;
      }

      setResetComplete(true);
      toast({
        title: "Contraseña actualizada",
        description: "Tu contraseña ha sido actualizada correctamente.",
      });
    } catch (error) {
      console.error('Unexpected error updating password:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: `Error inesperado: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <div className="flex flex-col items-center gap-2">
        <div className="flex h-8 w-8 items-center justify-center rounded-md">
          <Image
            src="/zee-short-log.svg"
            alt="Zeeguros Logo"
            className="h-8 w-8"
            width={32}
            height={32}
          />
        </div>
        <h1 className="text-xl font-bold">Restablecer contraseña</h1>
      </div>
      <div className="space-y-6">
        {!resetComplete ? (
          <>
            <p className="text-center text-sm text-gray-400">
              Ingresa tu nueva contraseña a continuación.
            </p>

            <form
              onSubmit={handleSubmit(onSubmit)}
              className="flex flex-col gap-6"
            >
              <div className="grid gap-2">
                <Label htmlFor="password">
                  Nueva contraseña
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    placeholder="••••••••"
                    type={showPassword ? "text" : "password"}
                    autoCapitalize="none"
                    autoComplete="new-password"
                    autoCorrect="off"
                    disabled={isLoading}
                    {...register("password")}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    style={{ color: '#3EA050' }}
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                    <span className="sr-only">
                      {showPassword ? "Ocultar contraseña" : "Mostrar contraseña"}
                    </span>
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">
                  Confirmar contraseña
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    placeholder="••••••••"
                    type={showConfirmPassword ? "text" : "password"}
                    autoCapitalize="none"
                    autoComplete="new-password"
                    autoCorrect="off"
                    disabled={isLoading}
                    {...register("confirmPassword")}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    style={{ color: '#3EA050' }}
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                    <span className="sr-only">
                      {showConfirmPassword ? "Ocultar contraseña" : "Mostrar contraseña"}
                    </span>
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? "Actualizando..." : "Actualizar contraseña"}
              </Button>
            </form>
          </>
        ) : (
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              Tu contraseña ha sido actualizada correctamente.
            </p>
            <Button
              onClick={() => router.push("/login")}
              className="w-full"
            >
              Ir a iniciar sesión
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
