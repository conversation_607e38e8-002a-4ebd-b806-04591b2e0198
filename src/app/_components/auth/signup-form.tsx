"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { createClient } from "@/utils/supabase/client";
import { signup } from "@/lib/actions/signup";
import { PhoneInput } from "@/components/ui/phone-input";

interface SignUpFormProps {
  className?: string;
}

export function SignUpForm({
  className,
}: SignUpFormProps) {
  const router = useRouter();
  const supabase = createClient();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string>("");
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [phoneNumber, setPhoneNumber] = useState<string>("");

  // Function to check if passwords match
  const checkPasswordsMatch = (e: React.FormEvent<HTMLFormElement>) => {
    const form = e.currentTarget;
    const password = form.password.value;
    const confirmPassword = form.confirmPassword.value;

    if (password !== confirmPassword) {
      setPasswordsMatch(false);
      e.preventDefault();
      return false;
    }

    setPasswordsMatch(true);
    return true;
  };

  const handleResendEmail = async () => {
    if (!userEmail) {
      setError("No se pudo determinar la dirección de correo. Por favor, intenta registrarte nuevamente.");
      return;
    }

    setIsLoading(true);

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: userEmail,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/confirm`,
        },
      });

      if (error) {
        console.error('Error resending confirmation email:', error);
        setError("No se pudo reenviar el correo de confirmación. Por favor, intenta más tarde.");
      } else {
        setError(null);
      }
    } catch (error) {
      console.error('Unexpected error resending email:', error);
      setError("Ocurrió un error al reenviar el correo. Por favor, intenta más tarde.");
    } finally {
      setIsLoading(false);
    }
  };

  if (emailSent) {
    return (
      <div className="p-6 space-y-6 text-center max-w-md mx-auto">
        <h2 className="text-xl font-semibold">
          Correo de confirmación
        </h2>
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
          <AlertCircle className="h-6 w-6 text-green-600" />
        </div>
        <p className="text-gray-600 mb-3">
          Te acabamos de enviar un enlace de confirmación a tu buzón. Puedes tardar hasta 5 minutos en recibirlo, recuerda revisar tu carpeta de <strong>SPAM</strong> 🗑️.
        </p>

        <div className="space-y-3">
          <Button
            onClick={handleResendEmail}
            className="btn-zeeguros w-full"
            disabled={isLoading}
          >
            Reenviar correo de confirmación
          </Button>

          <Button
            onClick={() => router.push("/login")}
            className="btn-zeeguros-back w-full"
          >
            Ir a iniciar sesión
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col gap-4 p-6 max-w-md mx-auto", className)}>
      <form
        onSubmit={(e) => {
          if (!checkPasswordsMatch(e)) {
            return;
          }
        }}
        action={async (formData: FormData) => {
          setIsLoading(true);
          setError(null);

          // Always use customer role
          formData.append('isBroker', 'false');

          try {
            const result = await signup(formData);

            if (!result.success) {
              setError(result.error || "Ocurrió un error al crear la cuenta. Por favor, inténtalo de nuevo.");
              setIsLoading(false);
              return;
            }

            // Store the email for potential resend
            if (result.email) {
              setUserEmail(result.email);
            }
            setEmailSent(true);
          } catch (error) {
            console.error('Sign-up error:', error);
            setError("Ocurrió un error al crear la cuenta. Por favor, inténtalo de nuevo.");
            setIsLoading(false);
          }
        }}
        className="flex flex-col gap-4"
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col items-center gap-2 mb-2">
            <div className="flex h-8 w-8 items-center justify-center rounded-md">
              <Image
                src="/zee-short-log.svg"
                alt="Zeeguros Logo"
                className="h-8 w-8"
                width={32}
                height={32}
              />
            </div>
            <h1 className="text-xl font-bold">
              Crear cuenta
            </h1>

          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="grid gap-1">
              <Label htmlFor="firstName" className="text-sm font-medium">
                Nombre{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="firstName"
                name="firstName"
                placeholder="Introduce tu nombre"
                type="text"
                disabled={isLoading}
                required
              />
            </div>

            <div className="grid gap-1">
              <Label htmlFor="lastName" className="text-sm font-medium">
                Apellido{" "}
                <span className="text-red-500">*</span>
              </Label>
              <Input
                id="lastName"
                name="lastName"
                placeholder="Introduce tu apellido"
                type="text"
                disabled={isLoading}
                required
              />
            </div>
          </div>

          <div className="grid gap-1">
            <Label htmlFor="phone" className="text-sm font-medium">
              Teléfono{" "}
              <span className="text-red-500">*</span>
            </Label>
            <PhoneInput
              id="phone"
              placeholder="Introduce tu número de teléfono"
              defaultCountry="ES"
              disabled={isLoading}
              required
              value={phoneNumber}
              onChange={(value) => setPhoneNumber(value || "")}
            />
            <input type="hidden" name="phone" value={phoneNumber} />
          </div>

          <div className="grid gap-1">
            <Label htmlFor="email" className="text-sm font-medium">
              Correo electrónico{" "}
              <span className="text-red-500">*</span>
            </Label>
            <Input
              id="email"
              name="email"
              placeholder="<EMAIL>"
              type="email"
              autoCapitalize="none"
              autoComplete="email"
              autoCorrect="off"
              disabled={isLoading}
              required
            />
          </div>

          <div className="grid gap-1">
            <Label htmlFor="password" className="text-sm font-medium">
              Contraseña{" "}
              <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="password"
                name="password"
                placeholder="••••••••"
                type={showPassword ? "text" : "password"}
                autoCapitalize="none"
                autoComplete="new-password"
                minLength={8}
                disabled={isLoading}
                required
              />

              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-2.5 text-muted-foreground hover:text-foreground"
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              La contraseña debe tener al menos 8 caracteres
            </p>
          </div>

          <div className="grid gap-1">
            <Label htmlFor="confirmPassword" className="text-sm font-medium">
              Confirmar contraseña{" "}
              <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                name="confirmPassword"
                placeholder="••••••••"
                type={showConfirmPassword ? "text" : "password"}
                autoCapitalize="none"
                autoComplete="new-password"
                disabled={isLoading}
                required
              />

              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-2.5 text-muted-foreground hover:text-foreground"
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
            {!passwordsMatch && (
              <p className="text-sm text-destructive flex items-center gap-1 mt-1">
                <AlertCircle className="h-4 w-4" />
                Las contraseñas no coinciden
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2 mt-1">
            <Checkbox
              id="acceptTerms"
              name="acceptTerms"
              checked={acceptTerms}
              onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
              disabled={isLoading}
              required
            />

            <label
              htmlFor="acceptTerms"
              className="text-sm text-muted-foreground"
            >
              Acepto la{" "}
              <Link
                href="https://zeeguros.com/politica-privacidad/"
                target="_blank"
                rel="noopener noreferrer"
                className="underline underline-offset-4 hover:text-zeeguros-green"
              >
                política de privacidad
              </Link>{" "}
              <span className="text-red-500">*</span>
            </label>
          </div>

          {error && (
            <div className="bg-destructive/10 text-destructive p-4 rounded-md flex items-start gap-3 border border-destructive/20">
              <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
              <div>
                <p className="font-medium mb-1">{error.includes('No se pudo conectar con el servidor') ? 'Problema de conexión' : 'Error al crear cuenta'}</p>
                <span className="text-sm">{error}</span>
              </div>
            </div>
          )}

          <Button
            type="submit"
            className="btn-zeeguros w-full mt-2"
            disabled={isLoading || !acceptTerms}
          >
            {isLoading ? "Creando cuenta..." : "Crear cuenta"}
          </Button>

          <div className="text-center text-sm mt-6 mb-2">
            ¿Ya tienes una cuenta?{" "}
            <Link
              href="/login"
              className="underline underline-offset-4 hover:text-zeeguros-green font-medium"
            >
              Iniciar sesión
            </Link>
          </div>
        </div>
      </form>
    </div>
  );
}
