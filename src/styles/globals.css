@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --zeeguros-green: 147 75% 56%;
    --zeeguros-green-foreground: 0 0% 9%;

    --zeeguros-accent: 147 75% 56%;
    --zeeguros-accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --zeeguros-green: 147 75% 56%;
    --zeeguros-green-foreground: 0 0% 98%;

    --zeeguros-accent: 147 75% 56%;
    --zeeguros-accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-4xl;
  }

  h2 {
    @apply text-3xl;
  }

  h3 {
    @apply text-2xl;
  }

  h4 {
    @apply text-xl;
  }
}

@layer components {
  .policy-card {
    @apply bg-card rounded-lg border border-border p-6 shadow-sm hover:shadow-lg transition-all duration-200 hover:scale-[1.02];
  }

  .form-container {
    @apply space-y-6 max-w-2xl mx-auto bg-card p-8 rounded-lg shadow-sm border border-border backdrop-blur-sm;
  }

  .onboarding-container {
    @apply max-w-4xl mx-auto bg-card p-8 rounded-lg shadow-sm border border-border backdrop-blur-sm;
  }

  .upload-area {
    @apply border-2 border-dashed border-border rounded-lg p-8 text-center cursor-pointer transition-all duration-200 hover:border-primary/50 hover:bg-muted hover:scale-[1.02];
  }

  .step-indicator {
    @apply flex items-center justify-center space-x-2 mb-8;
  }

  .step-dot {
    @apply w-3 h-3 rounded-full bg-muted;
  }

  .step-dot.active {
    @apply bg-primary;
  }

  .step-dot.completed {
    @apply bg-zeeguros-success;
  }

  /* Zeeguros specific components */
  .zeeguros-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .zeeguros-button {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2
    disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .zeeguros-card {
    @apply bg-white rounded-xl border border-border p-6 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .zeeguros-section {
    @apply py-12;
  }

  .zeeguros-input {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm
    ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium
    placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2
    focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Sidebar collapsed icon perfect alignment */
  [data-collapsible="icon"] [data-sidebar="menu-button"] {
    @apply !flex !items-center !justify-center !w-8 !h-8 !p-0 !mx-auto;
  }

  [data-collapsible="icon"] [data-sidebar="menu-item"] {
    @apply !flex !justify-center;
  }

  [data-collapsible="icon"] [data-sidebar="header"] {
    @apply !flex !justify-center !items-center;
  }

  [data-collapsible="icon"] [data-sidebar="footer"] {
    @apply !flex !justify-center !items-center;
  }

  /* Zeeguros Green Color Classes */
  .bg-zeeguros-green {
    background-color: hsl(var(--zeeguros-green));
  }

  .text-zeeguros-green {
    color: hsl(var(--zeeguros-green));
  }

  .border-zeeguros-green {
    border-color: hsl(var(--zeeguros-green));
  }

  .hover\:bg-zeeguros-green:hover {
    background-color: hsl(var(--zeeguros-green));
  }

  .hover\:text-zeeguros-green:hover {
    color: hsl(var(--zeeguros-green));
  }

  .focus\:ring-zeeguros-green:focus {
    --tw-ring-color: hsl(var(--zeeguros-green));
  }

  /* Zeeguros Accent Color Classes */
  .bg-zeeguros-accent {
    background-color: hsl(var(--zeeguros-accent));
  }

  .text-zeeguros-accent {
    color: hsl(var(--zeeguros-accent));
  }

  .border-zeeguros-accent {
    border-color: hsl(var(--zeeguros-accent));
  }

  /* Zeeguros Button Style - matches website design */
  .btn-zeeguros {
    @apply bg-zeeguros-green text-black font-semibold px-6 py-3 rounded-lg border-2 border-black
           hover:bg-zeeguros-green/90 transition-all duration-200
           focus:outline-none focus:ring-2 focus:ring-zeeguros-green focus:ring-offset-2;
  }

  /* Ensure btn-zeeguros overrides default button styles */
  button.btn-zeeguros,
  .btn-zeeguros {
    background-color: hsl(var(--zeeguros-green)) !important;
    color: black !important;
    border: 2px solid black !important;
    border-radius: 0.5rem !important;
  }

  .btn-zeeguros:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  /* Zeeguros Back Button Style - white background with same border and radius */
  .btn-zeeguros-back {
    @apply bg-white text-black font-semibold px-6 py-3 rounded-lg border-2 border-black
           hover:bg-gray-50 transition-all duration-200
           focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2;
  }

  /* Ensure btn-zeeguros-back overrides default button styles */
  button.btn-zeeguros-back,
  .btn-zeeguros-back {
    background-color: white !important;
    color: black !important;
    border: 2px solid black !important;
    border-radius: 0.5rem !important;
  }

  .btn-zeeguros-back:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #f9fafb !important;
  }

  /* Custom green color for icons */
  .icon-green-custom {
    color: #3ea050 !important;
  }
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
