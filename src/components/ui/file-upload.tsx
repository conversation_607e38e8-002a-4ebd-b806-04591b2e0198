import { useState, useRef, useCallback } from "react";
import {
  Upload,
  X,
  Check,
  AlertCircle,
  FileText,
  ArrowRight,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/ui/use-toast";
import { extractDataWithGemini, getMockPolicyData } from "@/lib/gemini-api";

interface FileUploadProps {
  onUploadComplete?: (file: File) => void;
  onContinue?: () => void;
}

export function FileUpload({ onUploadComplete, onContinue }: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [progress, setProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadComplete, setUploadComplete] = useState(false);
  const [extractionComplete, setExtractionComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useFallbackData, setUseFallbackData] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const extractionToastShownRef = useRef(false);
  const { toast } = useToast();

  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  const ALLOWED_FILE_TYPES = ["application/pdf", "image/jpeg", "image/png"];

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const validateFile = (file: File): boolean => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setError("Tipo de archivo inválido. Por favor sube un PDF, JPG o PNG.");
      toast({
        variant: "destructive",
        title: "Tipo de archivo inválido",
        description: "Por favor sube un PDF, JPG o PNG.",
      });
      return false;
    }

    if (file.size > MAX_FILE_SIZE) {
      setError("El tamaño del archivo excede el límite de 10MB.");
      toast({
        variant: "destructive",
        title: "Archivo demasiado grande",
        description: "Por favor sube un archivo menor a 10MB.",
      });
      return false;
    }

    return true;
  };

  const handleFileSelect = (selectedFile: File) => {
    setError(null);
    if (!validateFile(selectedFile)) return;

    setFile(selectedFile);
    simulateUpload(selectedFile);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const simulateUpload = (file: File) => {
    setIsUploading(true);
    setProgress(0);
    setError(null);
    setUseFallbackData(false);

    // Simulate upload progress
    const interval = setInterval(() => {
      setProgress((prevProgress) => {
        const newProgress = prevProgress + 10;

        if (newProgress >= 100) {
          clearInterval(interval);
          setIsUploading(false);
          setUploadComplete(true);

          // Start processing with Gemini AI
          processFileWithGemini(file);

          return 100;
        }

        return newProgress;
      });
    }, 300);
  };

  const useMockData = () => {
    setUseFallbackData(true);
    toast({
      title: "Usando datos predeterminados",
      description: "Continuando con datos de ejemplo para este demo.",
    });

    // Store mock data in local storage
    const mockData = getMockPolicyData();
    localStorage.setItem("extractedPolicyData", JSON.stringify(mockData));

    setExtractionComplete(true);
    // Marcamos que ya se mostró un toast para evitar duplicados
    extractionToastShownRef.current = true;

    if (onUploadComplete && file) {
      onUploadComplete(file);
    }
  };

  // Función para mostrar toast de extracción completada (solo una vez)
  const showExtractionCompleteToast = useCallback(() => {
    if (!extractionToastShownRef.current) {
      toast({
        title: "Extracción completada",
        description: "Hemos extraído la información de tu póliza con éxito.",
      });
      extractionToastShownRef.current = true;
    }
  }, [toast]);

  const processFileWithGemini = async (file: File) => {
    try {
      setIsProcessing(true);

      // Convert file to base64 for sending to API
      const reader = new FileReader();
      reader.readAsDataURL(file);

      reader.onloadend = async () => {
        const base64data = reader.result as string;

        try {
          // Extract policy data using Gemini API
          await extractDataWithGemini(base64data);

          // Actualizamos el estado
          setExtractionComplete(true);

          // Mostramos el toast (la función se encarga de verificar que solo se muestre una vez)
          showExtractionCompleteToast();

          if (onUploadComplete) {
            onUploadComplete(file);
          }
        } catch (error) {
          console.error("Error extracting data:", error);
          setError(
            "Error al procesar el documento. Puedes continuar con datos de ejemplo.",
          );
          toast({
            variant: "destructive",
            title: "Error de procesamiento",
            description:
              "No pudimos extraer la información de tu póliza. Puedes continuar con datos de ejemplo.",
          });
        } finally {
          setIsProcessing(false);
        }
      };
    } catch (error) {
      console.error("Error processing file:", error);
      setError(
        "Error al procesar el archivo. Puedes continuar con datos de ejemplo.",
      );
      setIsProcessing(false);

      toast({
        variant: "destructive",
        title: "Error de procesamiento",
        description:
          "Ocurrió un error al procesar tu archivo. Puedes continuar con datos de ejemplo.",
      });
    }
  };

  const handleCancel = () => {
    setFile(null);
    setProgress(0);
    setIsUploading(false);
    setIsProcessing(false);
    setUploadComplete(false);
    setExtractionComplete(false);
    setError(null);
    setUseFallbackData(false);
    extractionToastShownRef.current = false; // Resetear el estado del toast

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleContinue = () => {
    if (onContinue) {
      onContinue();
    }
  };

  return (
    <div className="space-y-6">
      {!file ? (
        <div
          className={`flex flex-col items-center justify-center border-2 border-dashed rounded-lg p-12 transition-all ${
            isDragging
              ? "border-primary bg-primary/5"
              : "border-muted-foreground/30"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileInputChange}
            className="hidden"
            accept=".pdf,.jpg,.jpeg,.png"
          />

          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-primary/10 p-3">
              <Upload className="h-6 w-6 text-primary" />
            </div>
            <div className="space-y-2 text-center">
              <h3 className="text-lg font-medium">
                Arrastra y suelta o haz clic para subir
              </h3>
              <p className="text-sm text-muted-foreground">
                Formatos soportados: PDF, JPG, PNG (máx 10MB)
              </p>
            </div>
            <Button type="button" className="btn-zeeguros">Seleccionar Archivo</Button>
          </div>
        </div>
      ) : (
        <div className="rounded-lg border p-6 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="rounded-full bg-primary/10 p-2">
                <FileText className="h-5 w-5 text-primary" />
              </div>
              <div>
                <p className="font-medium">{file.name}</p>
                <p className="text-sm text-muted-foreground">
                  {(file.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleCancel}
              disabled={isUploading || isProcessing}
              title="Cancelar subida"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {isUploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Subiendo...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}

          {isProcessing && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Procesando documento con IA...</span>
              </div>
              <Progress value={undefined} className="h-2 animate-pulse" />
            </div>
          )}

          {uploadComplete && !isProcessing && !error && (
            <div className="flex items-center text-green-600 space-x-2">
              <Check className="h-5 w-5" />
              <span>
                Documento {extractionComplete ? "procesado" : "subido"} con
                éxito
              </span>
            </div>
          )}

          {error && (
            <div className="flex flex-col space-y-4">
              <div className="flex items-start text-destructive space-x-2">
                <AlertCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />

                <span>{error}</span>
              </div>

              <Button
                variant="outline"
                className="w-full"
                onClick={useMockData}
              >
                Continuar con datos de ejemplo
              </Button>
            </div>
          )}

          {(extractionComplete || useFallbackData) && (
            <Button className="w-full" onClick={handleContinue}>
              Continuar a Revisión de Datos
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
