"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import {
  FileText,
  Gavel,
  Settings,
  HelpCircle,
} from "lucide-react"
import Image from "next/image"

import { NavUser } from "@/components/nav-user"
import {
  <PERSON>bar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useUser } from "@/hooks/use-user"
import { extractUserName } from "@/lib/user-metadata"


const navigationItems = [
  {
    title: "Mis Pólizas",
    url: "/policies",
    icon: FileText,
  },
  {
    title: "Mis Subastas",
    url: "/auctions",
    icon: Gavel,
  },
  {
    title: "Configuración",
    url: "/settings",
    icon: Settings,
  },
  {
    title: "Soporte",
    url: "/support",
    icon: HelpCircle,
  },
]

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  userRole?: string;
}

export function AppSidebar({ userRole = "customer", ...props }: AppSidebarProps) {
  const { user, role, loading } = useUser();
  const [userName, setUserName] = useState("Usuario");
  const [userEmail, setUserEmail] = useState("<EMAIL>");
  const [userRoleDisplay, setUserRoleDisplay] = useState("Plataforma de seguros");

  useEffect(() => {
    if (user) {
      const { fullName } = extractUserName(user);
      setUserName(fullName);
      setUserEmail(user.email || "<EMAIL>");

      // Set role display based on user role from database
      if (role) {
        switch (role) {
          case "customer":
            setUserRoleDisplay("Cliente");
            break;
          case "admin":
            setUserRoleDisplay("Administrador");
            break;
          case "agent":
            setUserRoleDisplay("Agente");
            break;
          default:
            setUserRoleDisplay("Plataforma de seguros");
        }
      }
    }
  }, [user, role]);

  if (loading) {
    return (
      <Sidebar variant="inset" {...props}>
        <SidebarHeader>
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
          </div>
        </SidebarHeader>
      </Sidebar>
    );
  }

  return (
    <Sidebar variant="inset" collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/policies">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-black text-white">
                  <Image
                    src="/zee-short-logo-white.svg"
                    alt="Zeeguros"
                    width={20}
                    height={20}
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:sr-only">
                  <span className="truncate font-semibold">Zeeguros</span>
                  <span className="truncate text-xs">{userRoleDisplay}</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarMenu className="gap-2">
          {navigationItems.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild tooltip={item.title}>
                <a href={item.url} className="font-medium">
                  <item.icon className="size-4 shrink-0 text-zeeguros-green" />
                  <span className="group-data-[collapsible=icon]:sr-only">{item.title}</span>
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={{
          name: userName,
          email: userEmail,
          avatar: "/avatars/user.jpg"
        }} />
      </SidebarFooter>
    </Sidebar>
  )
}
