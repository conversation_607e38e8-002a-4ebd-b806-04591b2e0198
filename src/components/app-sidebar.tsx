"use client"

import * as React from "react"
import { useEffect, useState } from "react"
import {
  User,
  FileText,
  Gavel,
  Settings,
  HelpCircle,
} from "lucide-react"
import Image from "next/image"

import { NavUser } from "@/components/nav-user"
import {
  Side<PERSON>,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { useUser } from "@/lib/user-metadata"

const navigationItems = [
  {
    title: "Mis Pólizas",
    url: "/dashboard/policies",
    icon: FileText,
  },
  {
    title: "Mis Subastas",
    url: "/dashboard/auctions",
    icon: Gavel,
  },
  {
    title: "Configuración",
    url: "/dashboard/settings",
    icon: Settings,
  },
  {
    title: "Soporte",
    url: "/dashboard/support",
    icon: HelpCircle,
  },
]

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {}

export function AppSidebar(props: AppSidebarProps) {
  const { user, role } = useUser();
  const [userName, setUserName] = useState("Usuario");
  const [userEmail, setUserEmail] = useState("<EMAIL>");
  const [userRoleDisplay, setUserRoleDisplay] = useState("Plataforma de seguros");

  useEffect(() => {
    if (user) {
      setUserName(user.user_metadata?.full_name || "Usuario");
      setUserEmail(user.email || "<EMAIL>");
      if (role) {
        setUserRoleDisplay(role === "customer" ? "Plataforma de seguros" : role);
      }
    }
  }, [user, role]);

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-black text-white">
                  <Image
                    src="/zee-short-log.svg"
                    alt="Zeeguros"
                    width={20}
                    height={20}
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Zeeguros</span>
                  <span className="truncate text-xs">{userRoleDisplay}</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarMenu className="gap-2">
          {navigationItems.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild>
                <a href={item.url} className="font-medium">
                  <item.icon className="size-4" />
                  {item.title}
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={{ name: userName, email: userEmail, avatar: "/avatars/user.jpg" }} />
      </SidebarFooter>
    </Sidebar>
  )
}
