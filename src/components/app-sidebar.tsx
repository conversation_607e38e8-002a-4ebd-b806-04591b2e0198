"use client"

import * as React from "react"
import {
  User,
  FileText,
  Gavel,
  Settings,
  HelpCircle,
} from "lucide-react"
import Image from "next/image"

import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"


const navigationItems = [
  {
    title: "Tomador",
    url: "/dashboard/tomador",
    icon: User,
  },
  {
    title: "Mis Pólizas",
    url: "/dashboard/policies",
    icon: FileText,
  },
  {
    title: "Mis Subastas",
    url: "/dashboard/auctions",
    icon: Gavel,
  },
  {
    title: "Configuración",
    url: "/dashboard/settings",
    icon: Settings,
  },
  {
    title: "Soporte",
    url: "/dashboard/support",
    icon: HelpCircle,
  },
]

const data = {
  user: {
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  userRole?: string;
}

export function AppSidebar({ userRole = "customer", ...props }: AppSidebarProps) {

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-black text-white">
                  <Image
                    src="/zee-short-log.svg"
                    alt="Zeeguros"
                    width={20}
                    height={20}
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Zeeguros</span>
                  <span className="truncate text-xs">Plataforma de seguros</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarMenu className="gap-2">
          {navigationItems.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild>
                <a href={item.url} className="font-medium">
                  <item.icon className="size-4" />
                  {item.title}
                </a>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
