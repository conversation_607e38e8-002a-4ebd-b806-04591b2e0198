"use client"

import * as React from "react"
import {
  Plus,
  Car,
  Bike,
  ChevronLeft,
  ChevronRight,
} from "lucide-react"
import Image from "next/image"

import { NavUser } from "@/components/nav-user"
import {
  Sidebar,
  SidebarContent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"

// Mock auction data - replace with real data
const mockAuctions = [
  {
    id: 1,
    type: "car",
    title: "Seguro Coche",
    code: "A2030JM",
    date: "2025/06/12",
    status: "Activa",
    icon: Car,
  },
  {
    id: 2,
    type: "bike",
    title: "Seguro Moto",
    code: "B3055JVM",
    date: "2025/06/12",
    status: "Activa",
    icon: Bike,
  }
]

// For testing empty state, set this to true
const SHOW_EMPTY_STATE = false

const data = {
  user: {
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/avatars/user.jpg",
  },
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  userRole?: string;
}

export function AppSidebar({ userRole = "customer", ...props }: AppSidebarProps) {
  const [currentPage, setCurrentPage] = React.useState(1)
  const auctionsPerPage = 2
  const displayAuctions = SHOW_EMPTY_STATE ? [] : mockAuctions
  const totalAuctions = displayAuctions.length
  const totalPages = Math.ceil(totalAuctions / auctionsPerPage)

  const startIndex = (currentPage - 1) * auctionsPerPage
  const currentAuctions = displayAuctions.slice(startIndex, startIndex + auctionsPerPage)

  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1)
    }
  }

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }

  return (
    <Sidebar variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <a href="/dashboard">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-black text-white">
                  <Image
                    src="/zee-short-log.svg"
                    alt="Zeeguros"
                    width={20}
                    height={20}
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Zeeguros</span>
                  <span className="truncate text-xs">Plataforma de seguros</span>
                </div>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <div className="flex items-center justify-between px-2 py-1">
            <SidebarGroupLabel className="text-sm font-medium">
              Mis Subastas
            </SidebarGroupLabel>
            <Button size="sm" className="h-6 px-2 text-xs">
              <Plus className="h-3 w-3 mr-1" />
              Crear
            </Button>
          </div>

          <SidebarGroupContent>
            {totalAuctions === 0 ? (
              <div className="px-2 py-4 text-center">
                <div className="rounded-lg border-2 border-dashed border-muted-foreground/25 p-6">
                  <p className="text-sm text-muted-foreground">
                    Aún no hay subastas.
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    ¡Crea una ya!
                  </p>
                </div>
              </div>
            ) : (
              <SidebarMenu>
                {currentAuctions.map((auction) => (
                  <SidebarMenuItem key={auction.id}>
                    <SidebarMenuButton asChild className="h-auto p-3">
                      <a href={`/auctions/${auction.id}`}>
                        <div className="flex items-start gap-3 w-full">
                          <div className="flex-shrink-0 mt-0.5">
                            <auction.icon className="h-4 w-4" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium truncate">
                                {auction.title}
                              </span>
                              <span className="text-xs bg-green-100 text-green-800 px-1.5 py-0.5 rounded">
                                Activa
                              </span>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {auction.code}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {auction.date}
                            </div>
                          </div>
                        </div>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            )}
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        {totalAuctions > 0 && (
          <div className="px-2 py-2 border-t">
            <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
              <span>Subastas: {totalAuctions}</span>
              {totalPages > 1 && (
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={prevPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-3 w-3" />
                  </Button>
                  <span className="text-xs">
                    {currentPage} / {totalPages}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={nextPage}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  )
}
