# Changelog - 2025-06-11

## Added Zeeguros Brand Green Color and Button Style

### Overview
Implemented the official Zeeguros green color (`#06BE60`) and button style from the main website into the platform's design system.

### Changes Made

#### 1. CSS Variables and Classes
- **Added CSS custom properties** in `src/styles/globals.css`:
  - `--zeeguros-green: 147 75% 56%`
  - `--zeeguros-green-foreground: 0 0% 9%` (light mode)
  - `--zeeguros-green-foreground: 0 0% 98%` (dark mode)

- **Added utility classes**:
  - `.bg-zeeguros-green` - Green background
  - `.text-zeeguros-green` - Green text
  - `.border-zeeguros-green` - Green border
  - `.hover:bg-zeeguros-green:hover` - Green background on hover
  - `.hover:text-zeeguros-green:hover` - Green text on hover
  - `.focus:ring-zeeguros-green:focus` - Green focus ring

- **Added Zeeguros button style** (`.btn-zeeguros`):
  - Green background with black text
  - Rounded corners (`rounded-lg`) design
  - Black border (2px)
  - Hover effects with lift and shadow
  - Focus ring for accessibility
  - Added `!important` rules to ensure style override

- **Updated Zeeguros accent color** (changed from `#3AE386` to `#06BE60`):
  - CSS variables: `--zeeguros-accent` and `--zeeguros-accent-foreground`
  - Utility classes: `.bg-zeeguros-accent`, `.text-zeeguros-accent`, `.border-zeeguros-accent`
  - Used for completed steps in policy creation flow
  - Now matches the official Zeeguros green color

#### 2. Tailwind Configuration
- **Updated `tailwind.config.ts`**:
  - Added `green: '#06BE60'` to zeeguros color palette
  - Added `zeeguros-green` color with DEFAULT and foreground variants
  - Enabled usage of `bg-zeeguros-green`, `text-zeeguros-green`, etc.

#### 3. UI Component Updates
- **Settings Page** (`src/app/settings/page.tsx`):
  - Applied Zeeguros button style to "Guardar Cambios" button
  - Applied Zeeguros button style to "Actualizar Contraseña" button

- **Policies Page** (`src/app/policies/page.tsx`):
  - Applied Zeeguros button style to "Añadir Nueva Póliza" button

- **New Policy Flow** (`src/app/policies/new-policy/_components/PolicyReview.tsx`):
  - Applied Zeeguros button style to "Confirmar y crear subasta" button
  - Replaced red background with official Zeeguros green style

- **Policy Stepper** (`src/app/policies/new-policy/_components/PolicyStepper.tsx`):
  - Updated completed step circles to use Zeeguros green color `#06BE60`
  - Fixed progress line to show uniform background across all steps
  - Added animated progress line that fills based on current step
  - Enhanced visual feedback for step progression

- **File Upload Component** (`src/components/ui/file-upload.tsx`):
  - Applied Zeeguros button style to "Seleccionar Archivo" button
  - Applied Zeeguros button style to "Continuar a Revisión de Datos" button
  - Consistent branding across file upload interface

- **Progress Component** (`src/components/ui/progress.tsx`):
  - Updated progress bar color to use Zeeguros green `#06BE60`
  - Consistent branding across all progress indicators

- **Policy Data Form** (`src/app/policies/new-policy/_components/PolicyDataForm.tsx`):
  - Applied Zeeguros button style to all "Siguiente" and "Continuar" buttons
  - Consistent navigation button styling across all form tabs

- **Tabs Component** (`src/components/ui/tabs.tsx`):
  - Updated active tab styling to use Zeeguros green background with black text
  - Inactive tabs display in gray for better visual hierarchy
  - Enhanced font weight for active tabs

- **Auction Duration Select** (`src/app/policies/new-policy/_components/AuctionDurationSelect.tsx`):
  - Updated selection colors from red to Zeeguros green `#06BE60`
  - Applied Zeeguros button style to "Continuar" button
  - Enhanced visual feedback for selected duration options

### 🔧 **Infrastructure & Storage**

- **Supabase Storage Configuration**:
  - Created `policy_documents` storage bucket for file uploads
  - Configured bucket with 10MB file size limit
  - Allowed MIME types: PDF, JPEG, PNG, JPG
  - Implemented Row Level Security (RLS) policies for user data isolation
  - Users can only upload, view, and delete their own policy documents

- **File Upload Security Fix** (`src/app/policies/new-policy/_hooks/useNewPolicy.ts`):
  - Fixed "Invalid key" error by implementing filename sanitization
  - Removes special characters, spaces, and invalid characters from filenames
  - Preserves file extensions while ensuring Supabase Storage compatibility
  - Prevents upload failures due to invalid filename characters

- **Policy Review Component** (`src/app/policies/new-policy/_components/PolicyReview.tsx`):
  - Updated accordion titles to use Zeeguros green background with black text
  - Applied bold font weight for better visual hierarchy
  - Consistent styling across all accordion sections

- **Policy Stepper Component** (`src/app/policies/new-policy/_components/PolicyStepper.tsx`):
  - Fixed step 5 (Success) to show green check mark instead of number
  - Applied Zeeguros accent color to active step
  - Consistent visual feedback for completed and final steps

- **Policy Success Component** (`src/app/policies/new-policy/_components/PolicySuccess.tsx`):
  - Updated "Ver mi subasta" button to use standard Zeeguros button styling
  - Replaced red background with btn-zeeguros class for brand consistency

- **Button Styling Consistency** (`src/styles/globals.css`):
  - Created new `btn-zeeguros-back` class for "Atrás" buttons
  - White background with same border radius and black border as primary buttons
  - Consistent hover effects and focus states
  - Applied to all back buttons across the new policy flow

- **Back Button Updates**:
  - `PolicyDataForm.tsx`: Updated all 4 "Atrás" buttons to use new styling
  - `AuctionDurationSelect.tsx`: Updated "Atrás" button styling
  - `PolicyReview.tsx`: Updated "Atrás" button styling
  - Consistent visual hierarchy with white background and black border

- **User Avatar Styling** (`src/components/nav-user.tsx`):
  - Updated user avatar fallback to use Zeeguros green background
  - Changed text color to black for better contrast
  - Added font-semibold for better readability
  - Applied to both sidebar and dropdown menu avatars

- **Sidebar Icons Styling** (`src/components/app-sidebar.tsx`):
  - Updated all navigation icons to use custom green color #3ea050
  - Applied inline style with specific hex color to FileText, Gavel, Settings, and HelpCircle icons
  - Consistent custom green color across all sidebar navigation elements

- **Sidebar Trigger Button** (`src/components/ui/sidebar.tsx`):
  - Updated SidebarTrigger component to use custom green color #3ea050
  - Applied inline style to PanelLeft icon for collapse/expand functionality
  - Consistent color with other sidebar navigation icons

- **Policy "Ver Detalles" Buttons** (`src/app/policies/page.tsx`):
  - Updated all "Ver Detalles" buttons to use btn-zeeguros-back styling
  - Applied white background with black border and rounded corners
  - Consistent styling across all policy tabs (Todas, Coche, Moto)
  - Maintains ArrowRight icon for visual hierarchy

- **Support Page Redesign** (`src/app/support/page.tsx`):
  - Updated contact buttons to use btn-zeeguros primary styling
  - Added mailto functionality to email button (<EMAIL>)
  - Added WhatsApp functionality with "Chatear Ahora" button (https://wa.me/34665444222)
  - Changed phone icon to MessageCircle icon for WhatsApp
  - Updated section title from "Teléfono" to "WhatsApp"
  - Updated description to reflect chat functionality
  - Removed "Preguntas Frecuentes" section completely
  - Maintained 2-column grid layout for contact options

- **Hydration Error Fix** (`src/styles/globals.css`, `src/components/ui/sidebar.tsx`, `src/components/app-sidebar.tsx`):
  - Created `.icon-green-custom` CSS class to replace inline styles
  - Fixed hydration mismatch error caused by inline style differences
  - Applied consistent #3ea050 color through CSS class instead of inline styles
  - Updated SidebarTrigger and navigation icons to use CSS class

- **Policy Cards Design Enhancement** (`src/app/policies/page.tsx`, `src/styles/globals.css`):
  - Enhanced visual design with better contrast and colors
  - Added left border accent in Zeeguros green (#06BE60)
  - Implemented subtle gradient background (white to gray-50/30)
  - Redesigned icon containers with rounded corners and green accent
  - Improved typography with semibold titles and better spacing
  - Added styled policy number badges with gray background
  - Enhanced expiration date display with subtle gray accent background
  - Added smooth hover animations with shadow elevation
  - Created Zeeguros color utility classes for consistent branding
  - Applied changes across all policy tabs (Todas, Coche, Moto)

- **Login Form Styling Update** (`src/app/_components/auth/auth-login-form.tsx`, `src/lib/actions/login.ts`):
  - Updated login button to use btn-zeeguros styling (green Zeeguros button)
  - Changed default redirect destination from /dashboard to /policies
  - Maintains consistent branding with new look and feel
  - Login button now matches the primary action button style

- **Support Page** (`src/app/support/page.tsx`):
  - Removed "Chat en Vivo" option as requested
  - Updated grid layout from 3 columns to 2 columns
  - Removed unused MessageCircle import

#### 4. Documentation
- **Created comprehensive documentation** (`docs/zeeguros-green-color.md`):
  - Color values and CSS variables
  - Tailwind class usage examples
  - Best practices and accessibility guidelines
  - Current implementation status

### Technical Details

#### Color Specifications
- **Hex**: `#06BE60`
- **HSL**: `hsl(147, 75%, 56%)`
- **RGB**: `rgb(6, 190, 96)`

#### Usage Pattern
```tsx
// Zeeguros style button (recommended)
<Button className="btn-zeeguros">
  ¡Ahorrar ya!
</Button>

// Custom button with individual classes
<Button className="bg-zeeguros-green text-black border-2 border-black rounded-lg">
  Action Button
</Button>
```

### Benefits
1. **Brand Consistency**: Matches the official Zeeguros website color scheme
2. **Design System**: Provides standardized green color across the platform
3. **Accessibility**: Maintains good contrast ratios for readability
4. **Developer Experience**: Easy-to-use Tailwind classes and CSS variables

### Next Steps
- Consider applying the green color to other primary actions throughout the platform
- Evaluate additional brand colors from the main website for implementation
- Update component library documentation with new color guidelines

---

**Files Modified:**
- `src/styles/globals.css`
- `tailwind.config.ts`
- `src/app/settings/page.tsx`
- `src/app/policies/page.tsx`
- `src/app/support/page.tsx`
- `src/app/policies/new-policy/_components/PolicyReview.tsx`
- `src/app/policies/new-policy/_components/PolicyStepper.tsx`
- `src/components/ui/file-upload.tsx`
- `src/components/ui/progress.tsx`
- `src/app/policies/new-policy/_components/PolicyDataForm.tsx`
- `src/components/ui/tabs.tsx`
- `src/app/policies/new-policy/_components/AuctionDurationSelect.tsx`
- `src/app/policies/new-policy/_components/PolicyReview.tsx`
- `src/app/policies/new-policy/_hooks/useNewPolicy.ts`
- `src/app/policies/new-policy/_components/PolicyStepper.tsx`
- `src/app/policies/new-policy/_components/PolicySuccess.tsx`
- `src/styles/globals.css`
- `src/app/policies/new-policy/_components/PolicyDataForm.tsx`
- `src/app/policies/new-policy/_components/AuctionDurationSelect.tsx`
- `src/components/nav-user.tsx`
- `src/components/app-sidebar.tsx`
- `src/components/ui/sidebar.tsx`
- `src/app/policies/page.tsx`

**Files Added:**
- `docs/zeeguros-green-color.md`

**Files Removed:**
- `src/app/demo/page.tsx` (temporary demo page)
- "Chat en Vivo" option from support page
