# Changelog - 2025-06-11

## Added Zeeguros Brand Green Color and Button Style

### Overview
Implemented the official Zeeguros green color (`#06BE60`) and button style from the main website into the platform's design system.

### Changes Made

#### 1. CSS Variables and Classes
- **Added CSS custom properties** in `src/styles/globals.css`:
  - `--zeeguros-green: 147 75% 56%`
  - `--zeeguros-green-foreground: 0 0% 9%` (light mode)
  - `--zeeguros-green-foreground: 0 0% 98%` (dark mode)

- **Added utility classes**:
  - `.bg-zeeguros-green` - Green background
  - `.text-zeeguros-green` - Green text
  - `.border-zeeguros-green` - Green border
  - `.hover:bg-zeeguros-green:hover` - Green background on hover
  - `.hover:text-zeeguros-green:hover` - Green text on hover
  - `.focus:ring-zeeguros-green:focus` - Green focus ring

- **Added Zeeguros button style** (`.btn-zeeguros`):
  - Green background with black text
  - Rounded corners (`rounded-lg`) design
  - Black border (2px)
  - Hover effects with lift and shadow
  - Focus ring for accessibility
  - Added `!important` rules to ensure style override

- **Updated Zeeguros accent color** (changed from `#3AE386` to `#06BE60`):
  - CSS variables: `--zeeguros-accent` and `--zeeguros-accent-foreground`
  - Utility classes: `.bg-zeeguros-accent`, `.text-zeeguros-accent`, `.border-zeeguros-accent`
  - Used for completed steps in policy creation flow
  - Now matches the official Zeeguros green color

#### 2. Tailwind Configuration
- **Updated `tailwind.config.ts`**:
  - Added `green: '#06BE60'` to zeeguros color palette
  - Added `zeeguros-green` color with DEFAULT and foreground variants
  - Enabled usage of `bg-zeeguros-green`, `text-zeeguros-green`, etc.

#### 3. UI Component Updates
- **Settings Page** (`src/app/settings/page.tsx`):
  - Applied Zeeguros button style to "Guardar Cambios" button
  - Applied Zeeguros button style to "Actualizar Contraseña" button

- **Policies Page** (`src/app/policies/page.tsx`):
  - Applied Zeeguros button style to "Añadir Nueva Póliza" button

- **New Policy Flow** (`src/app/policies/new-policy/_components/PolicyReview.tsx`):
  - Applied Zeeguros button style to "Confirmar y crear subasta" button
  - Replaced red background with official Zeeguros green style

- **Policy Stepper** (`src/app/policies/new-policy/_components/PolicyStepper.tsx`):
  - Updated completed step circles to use Zeeguros green color `#06BE60`
  - Fixed progress line to show uniform background across all steps
  - Added animated progress line that fills based on current step
  - Enhanced visual feedback for step progression

- **File Upload Component** (`src/components/ui/file-upload.tsx`):
  - Applied Zeeguros button style to "Seleccionar Archivo" button
  - Applied Zeeguros button style to "Continuar a Revisión de Datos" button
  - Consistent branding across file upload interface

- **Progress Component** (`src/components/ui/progress.tsx`):
  - Updated progress bar color to use Zeeguros green `#06BE60`
  - Consistent branding across all progress indicators

- **Policy Data Form** (`src/app/policies/new-policy/_components/PolicyDataForm.tsx`):
  - Applied Zeeguros button style to all "Siguiente" and "Continuar" buttons
  - Consistent navigation button styling across all form tabs

- **Tabs Component** (`src/components/ui/tabs.tsx`):
  - Updated active tab styling to use Zeeguros green background with black text
  - Inactive tabs display in gray for better visual hierarchy
  - Enhanced font weight for active tabs

- **Support Page** (`src/app/support/page.tsx`):
  - Removed "Chat en Vivo" option as requested
  - Updated grid layout from 3 columns to 2 columns
  - Removed unused MessageCircle import

#### 4. Documentation
- **Created comprehensive documentation** (`docs/zeeguros-green-color.md`):
  - Color values and CSS variables
  - Tailwind class usage examples
  - Best practices and accessibility guidelines
  - Current implementation status

### Technical Details

#### Color Specifications
- **Hex**: `#06BE60`
- **HSL**: `hsl(147, 75%, 56%)`
- **RGB**: `rgb(6, 190, 96)`

#### Usage Pattern
```tsx
// Zeeguros style button (recommended)
<Button className="btn-zeeguros">
  ¡Ahorrar ya!
</Button>

// Custom button with individual classes
<Button className="bg-zeeguros-green text-black border-2 border-black rounded-lg">
  Action Button
</Button>
```

### Benefits
1. **Brand Consistency**: Matches the official Zeeguros website color scheme
2. **Design System**: Provides standardized green color across the platform
3. **Accessibility**: Maintains good contrast ratios for readability
4. **Developer Experience**: Easy-to-use Tailwind classes and CSS variables

### Next Steps
- Consider applying the green color to other primary actions throughout the platform
- Evaluate additional brand colors from the main website for implementation
- Update component library documentation with new color guidelines

---

**Files Modified:**
- `src/styles/globals.css`
- `tailwind.config.ts`
- `src/app/settings/page.tsx`
- `src/app/policies/page.tsx`
- `src/app/support/page.tsx`
- `src/app/policies/new-policy/_components/PolicyReview.tsx`
- `src/app/policies/new-policy/_components/PolicyStepper.tsx`
- `src/components/ui/file-upload.tsx`
- `src/components/ui/progress.tsx`
- `src/app/policies/new-policy/_components/PolicyDataForm.tsx`
- `src/components/ui/tabs.tsx`

**Files Added:**
- `docs/zeeguros-green-color.md`

**Files Removed:**
- `src/app/demo/page.tsx` (temporary demo page)
- "Chat en Vivo" option from support page
